1.根据下面的数据格式，更新TimeseriesService.proto文件
2.简化convertToInvestmentResponseProto函数
正常的数据格式
{
    "status": {
        "code": "200200",
        "message": "Process is successful with some data points errors"
    },
    "investments": [
        {
            "id": "E0GBR017IT",
            "values": [
                {
                    "datapointId": "HPD10",
                    "timeSeriesData": [
                        {
                            "date": "2024-02-29",
                            "value": "0.25376"
                        },
                        {
                            "date": "2024-03-31",
                            "value": "2.97969"
                        },
                        {
                            "date": "2024-04-30",
                            "value": "2.76010"
                        },
                        {
                            "date": "2024-05-31",
                            "value": "0.25376"
                        },
                        {
                            "date": "2024-06-30",
                            "value": "8.06765"
                        },
                        {
                            "date": "2024-07-31",
                            "value": "-4.16397"
                        },
                        {
                            "date": "2024-08-31",
                            "value": "0.25376"
                        },
                        {
                            "date": "2024-09-30",
                            "value": "-0.13255"
                        },
                        {
                            "date": "2024-10-31",
                            "value": "3.22690"
                        },
                        {
                            "date": "2024-11-30",
                            "value": "-0.20715"
                        }
                    ]
                }
            ]
        },
        {
            "id": "F00000PKCK",
            "errors": [
                {
                    "datapointId": "HPD10",
                    "errorCode": "403"
                }
            ]
        }
    ]
}
返回错误的数据格式
{
    "status": {
        "code": "401200",
        "message": "Token expired, Need to request a new token"
    }
}



