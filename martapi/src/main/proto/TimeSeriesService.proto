syntax = "proto3";

package timeseries;

import "TsCacheData.proto";

option java_multiple_files = true;
option java_package = "com.morningstar.martapi.grpc";
option java_outer_classname = "TimeSeriesServiceProto";

// Time Series Service Definition
service TimeSeriesService {
  // Retrieve time series data as protobuf (format=0)
  rpc GetTimeSeriesData(TimeSeriesRequest) returns (protobuf.TimeSeriesDatas);

  // Retrieve time series data as TSResponse (format=1)
  rpc RetrieveTimeSeriesData(TimeSeriesRequest) returns (protobuf.TimeSeriesDatas);

  // Get investment time series data as InvestmentResponse
  rpc GetInvestmentTSData(TimeSeriesRequest) returns (InvestmentResponseProto);
}

// Request message for time series data
message TimeSeriesRequest {
  repeated string investment_ids = 1;
  repeated string data_points = 2;
  string start_date = 3;
  string end_date = 4;
  string currency = 5;
  string pre_currency = 6;
  string read_cache = 7;
  string date_format = 8;
  string decimal_format = 9;
  string extend_performance = 10;
  string post_tax = 11;
  bool use_require_id = 12;
  string use_case = 13;
  bool use_new_ccs = 14;
  string product_id = 15;
  string request_id = 16;
  string trace_id = 17;
  string user_id = 18;
  string authorization = 19;
  bool check_entitlement = 20;
  string entitlement_product_id = 21;
}

// Investment Response message for gRPC
message InvestmentResponseProto {
  StatusProto status = 1;
  repeated InvestmentProto investments = 2;
}

// Status message
message StatusProto {
  string code = 1;
  string message = 2;
}

// Investment message
message InvestmentProto {
  string id = 1;
  repeated ValuePair values = 2;
  repeated string errors = 3;
}

// Generic value pair for investment data
message ValuePair {
  string key = 1;
  string value = 2;
  string date = 3;
  string code = 4;
}
