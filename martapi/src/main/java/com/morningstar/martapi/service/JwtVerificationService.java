package com.morningstar.martapi.service;

import com.auth0.jwk.Jwk;
import com.auth0.jwk.JwkException;
import com.auth0.jwk.JwkProvider;
import com.auth0.jwk.JwkProviderBuilder;
import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.auth0.jwt.interfaces.JWTVerifier;
import com.morningstar.martgateway.infrastructures.log.LogAttribute;
import com.morningstar.martgateway.infrastructures.log.LogEntity;
import com.morningstar.martgateway.infrastructures.log.LogEntry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

import java.security.interfaces.RSAPublicKey;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

@Slf4j
public class JwtVerificationService {

    private final JwkProvider jwkProvider;
    private final ConcurrentHashMap<String, JWTVerifier> verifierCache;

    public JwtVerificationService(@Value("${jwt.jwks.url}") String jwksUrl) {
        this.jwkProvider = new JwkProviderBuilder(jwksUrl)
                .cached(10, 24, TimeUnit.HOURS)
                .build();
        this.verifierCache = new ConcurrentHashMap<>();
    }

    public boolean verifyJwtToken(DecodedJWT jwt) {

        String keyId = jwt.getKeyId();
        if (keyId == null) {
            log.warn("JWT does not contain 'kid' header");
            return false;
        }

        JWTVerifier verifier = verifierCache.computeIfAbsent(keyId, k -> {
            try {
                Jwk jwk = jwkProvider.get(k);
                Algorithm algorithm = Algorithm.RSA256((RSAPublicKey) jwk.getPublicKey(), null);
                return JWT.require(algorithm).build();
            } catch (Exception e) {
                LogEntry.error(
                        new LogEntity(LogAttribute.EVENT_TYPE, "JWT_VERIFIER_BUILD_ERROR"),
                        new LogEntity(LogAttribute.EXCEPTION_TYPE, e.getClass()),
                        new LogEntity(LogAttribute.EXCEPTION_MESSAGE, e.getMessage())
                );
                return null;
            }
        });

        if (verifier == null) {
            log.warn("Verifier could not be created for kid: {}", keyId);
            return false;
        }

        verifier.verify(jwt);
        return true;

    }
}