package com.morningstar.martapi.grpc;

import com.google.protobuf.util.JsonFormat;
import com.morningstar.martapi.grpc.InvestmentResponseProto;
import com.morningstar.martapi.grpc.InvestmentProto;
import com.morningstar.martapi.grpc.InvestmentValueProto;
import com.morningstar.martapi.grpc.TimeSeriesDataPointProto;
import com.morningstar.martapi.grpc.StatusProto;

public class InvestmentResponseProtoDemo {
    
    public static void main(String[] args) throws Exception {
        InvestmentResponseProto response = createSampleResponse();
        
        String json = JsonFormat.printer()
                .includingDefaultValueFields()
                .print(response);
        
        System.out.println("Generated JSON structure:");
        System.out.println(json);
    }
    
    private static InvestmentResponseProto createSampleResponse() {
        StatusProto status = StatusProto.newBuilder()
                .setCode("200")
                .setMessage("Success")
                .build();
        
        TimeSeriesDataPointProto dataPoint1 = TimeSeriesDataPointProto.newBuilder()
                .setDate("2024-02-29")
                .setValue("0.25376")
                .build();
        
        TimeSeriesDataPointProto dataPoint2 = TimeSeriesDataPointProto.newBuilder()
                .setDate("2024-03-31")
                .setValue("2.97969")
                .build();
        
        TimeSeriesDataPointProto dataPoint3 = TimeSeriesDataPointProto.newBuilder()
                .setDate("2024-04-30")
                .setValue("2.76010")
                .build();
        
        TimeSeriesDataPointProto dataPoint4 = TimeSeriesDataPointProto.newBuilder()
                .setDate("2024-05-31")
                .setValue("0.25376")
                .build();
        
        TimeSeriesDataPointProto dataPoint5 = TimeSeriesDataPointProto.newBuilder()
                .setDate("2024-06-30")
                .setValue("8.06765")
                .build();
        
        TimeSeriesDataPointProto dataPoint6 = TimeSeriesDataPointProto.newBuilder()
                .setDate("2024-07-31")
                .setValue("-4.16397")
                .build();
        
        TimeSeriesDataPointProto dataPoint7 = TimeSeriesDataPointProto.newBuilder()
                .setDate("2024-08-31")
                .setValue("0.25376")
                .build();
        
        TimeSeriesDataPointProto dataPoint8 = TimeSeriesDataPointProto.newBuilder()
                .setDate("2024-09-30")
                .setValue("-0.13255")
                .build();
        
        TimeSeriesDataPointProto dataPoint9 = TimeSeriesDataPointProto.newBuilder()
                .setDate("2024-10-31")
                .setValue("3.22690")
                .build();
        
        TimeSeriesDataPointProto dataPoint10 = TimeSeriesDataPointProto.newBuilder()
                .setDate("2024-11-30")
                .setValue("-0.20715")
                .build();
        
        InvestmentValueProto investmentValue = InvestmentValueProto.newBuilder()
                .setDatapointId("HPD10")
                .addTimeSeriesData(dataPoint1)
                .addTimeSeriesData(dataPoint2)
                .addTimeSeriesData(dataPoint3)
                .addTimeSeriesData(dataPoint4)
                .addTimeSeriesData(dataPoint5)
                .addTimeSeriesData(dataPoint6)
                .addTimeSeriesData(dataPoint7)
                .addTimeSeriesData(dataPoint8)
                .addTimeSeriesData(dataPoint9)
                .addTimeSeriesData(dataPoint10)
                .build();
        
        InvestmentProto investment = InvestmentProto.newBuilder()
                .setId("E0GBR017IT")
                .addValues(investmentValue)
                .build();
        
        return InvestmentResponseProto.newBuilder()
                .setStatus(status)
                .addInvestments(investment)
                .build();
    }
}
