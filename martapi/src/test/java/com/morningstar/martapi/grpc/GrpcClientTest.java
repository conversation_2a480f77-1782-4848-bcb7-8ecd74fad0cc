package com.morningstar.martapi.grpc;

import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import io.grpc.StatusRuntimeException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;

import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * gRPC client test for TimeSeriesService.
 * Contains both unit tests for gRPC client setup and integration tests for live service calls.
 */
public class GrpcClientTest {

    private static final String DEFAULT_HOST = "localhost";
    private static final int DEFAULT_PORT = 9090;
    private static final int CONNECTION_TIMEOUT_SECONDS = 5;

    @Test
    @DisplayName("Test gRPC client channel creation and configuration")
    void testGrpcClientSetup() {
        ManagedChannel channel = null;
        try {
            channel = ManagedChannelBuilder.forAddress(DEFAULT_HOST, DEFAULT_PORT)
                    .usePlaintext()
                    .build();

            assertNotNull(channel, "gRPC channel should be created successfully");
            assertFalse(channel.isShutdown(), "Channel should not be shutdown initially");
            assertFalse(channel.isTerminated(), "Channel should not be terminated initially");

            TimeSeriesServiceGrpc.TimeSeriesServiceBlockingStub stub =
                TimeSeriesServiceGrpc.newBlockingStub(channel);
            assertNotNull(stub, "gRPC stub should be created successfully");

        } finally {
            if (channel != null) {
                channel.shutdown();
                try {
                    if (!channel.awaitTermination(5, TimeUnit.SECONDS)) {
                        channel.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    channel.shutdownNow();
                    Thread.currentThread().interrupt();
                }
            }
        }
    }

    @Test
    @DisplayName("Test TimeSeriesRequest builder functionality")
    void testRequestBuilder() {
        TimeSeriesRequest request = TimeSeriesRequest.newBuilder()
                .addInvestmentIds("FOUSA00DOU")
                .addInvestmentIds("FOUSA00ABC")
                .addDataPoints("3821")
                .addDataPoints("3822")
                .setStartDate("2023-01-01")
                .setEndDate("2023-12-31")
                .setRequestId("test-request-123")
                .setUserId("test-user")
                .setAuthorization("Bearer test-token")
                .setProductId("test-product")
                .setCurrency("USD")
                .setUseCase("test")
                .build();

        assertNotNull(request, "Request should be built successfully");
        assertEquals(2, request.getInvestmentIdsCount(), "Should have 2 investment IDs");
        assertEquals("FOUSA00DOU", request.getInvestmentIds(0), "First investment ID should match");
        assertEquals("FOUSA00ABC", request.getInvestmentIds(1), "Second investment ID should match");
        assertEquals(2, request.getDataPointsCount(), "Should have 2 data points");
        assertEquals("3821", request.getDataPoints(0), "First data point should match");
        assertEquals("test-request-123", request.getRequestId(), "Request ID should match");
        assertEquals("test-user", request.getUserId(), "User ID should match");
        assertEquals("Bearer test-token", request.getAuthorization(), "Authorization should match");
        assertEquals("test-product", request.getProductId(), "Product ID should match");
        assertEquals("USD", request.getCurrency(), "Currency should match");
        assertEquals("test", request.getUseCase(), "Use case should match");
    }

    @Test
    @Disabled("Integration test - requires running application on localhost:9090")
    @DisplayName("Integration test for gRPC service call - GetTimeSeriesData")
    void testGrpcServiceCall_GetTimeSeriesData() {
        ManagedChannel channel = createChannel();

        try {
            TimeSeriesServiceGrpc.TimeSeriesServiceBlockingStub stub =
                TimeSeriesServiceGrpc.newBlockingStub(channel)
                    .withDeadlineAfter(30, TimeUnit.SECONDS);

            TimeSeriesRequest request = createTestRequest();

            System.out.println("Making gRPC call to GetTimeSeriesData...");
            var response = stub.getTimeSeriesData(request);

            assertNotNull(response, "Response should not be null");
            System.out.println("Response received successfully: " + response.getValuesCount() + " time series data entries");

        } catch (StatusRuntimeException e) {
            if (e.getStatus().getCode() == io.grpc.Status.Code.UNAVAILABLE) {
                System.out.println("Service unavailable - this is expected when application is not running");
                System.out.println("To run this test: 1) Start the mart-api application, 2) Remove @Disabled annotation");
            } else {
                fail("Unexpected gRPC error: " + e.getStatus());
            }
        } finally {
            shutdownChannel(channel);
        }
    }

    @Test
    @Disabled("Integration test - requires running application on localhost:9090")
    @DisplayName("Integration test for gRPC service call - RetrieveTimeSeriesData")
    void testGrpcServiceCall_RetrieveTimeSeriesData() {
        ManagedChannel channel = createChannel();

        try {
            TimeSeriesServiceGrpc.TimeSeriesServiceBlockingStub stub =
                TimeSeriesServiceGrpc.newBlockingStub(channel)
                    .withDeadlineAfter(30, TimeUnit.SECONDS);

            TimeSeriesRequest request = createTestRequest();

            System.out.println("Making gRPC call to RetrieveTimeSeriesData...");
            var response = stub.retrieveTimeSeriesData(request);

            assertNotNull(response, "Response should not be null");
            System.out.println("Response received successfully: " + response.getValuesCount() + " time series data entries");

        } catch (StatusRuntimeException e) {
            if (e.getStatus().getCode() == io.grpc.Status.Code.UNAVAILABLE) {
                System.out.println("Service unavailable - this is expected when application is not running");
                System.out.println("To run this test: 1) Start the mart-api application, 2) Remove @Disabled annotation");
            } else {
                fail("Unexpected gRPC error: " + e.getStatus());
            }
        } finally {
            shutdownChannel(channel);
        }
    }

    @Test
    @Disabled("Integration test - requires running application on localhost:9090")
    @DisplayName("Integration test for gRPC service call - GetInvestmentTSData")
    void testGrpcServiceCall_GetInvestmentTSData() {
        ManagedChannel channel = createChannel();

        try {
            TimeSeriesServiceGrpc.TimeSeriesServiceBlockingStub stub =
                TimeSeriesServiceGrpc.newBlockingStub(channel)
                    .withDeadlineAfter(30, TimeUnit.SECONDS);

            TimeSeriesRequest request = createTestRequest();

            System.out.println("Making gRPC call to GetInvestmentTSData...");
            var response = stub.getInvestmentTSData(request);

            assertNotNull(response, "Response should not be null");
            assertNotNull(response.getStatus(), "Response status should not be null");
            System.out.println("Response received successfully with status: " + response.getStatus().getCode());
            System.out.println("Number of investments: " + response.getInvestmentsCount());

        } catch (StatusRuntimeException e) {
            if (e.getStatus().getCode() == io.grpc.Status.Code.UNAVAILABLE) {
                System.out.println("Service unavailable - this is expected when application is not running");
                System.out.println("To run this test: 1) Start the mart-api application, 2) Remove @Disabled annotation");
            } else {
                fail("Unexpected gRPC error: " + e.getStatus());
            }
        } finally {
            shutdownChannel(channel);
        }
    }

    private ManagedChannel createChannel() {
        return ManagedChannelBuilder.forAddress(DEFAULT_HOST, DEFAULT_PORT)
                .usePlaintext()
                .keepAliveTime(30, TimeUnit.SECONDS)
                .keepAliveTimeout(5, TimeUnit.SECONDS)
                .keepAliveWithoutCalls(true)
                .maxInboundMessageSize(10 * 1024 * 1024) // 10MB
                .build();
    }

    private TimeSeriesRequest createTestRequest() {
        return TimeSeriesRequest.newBuilder()
                .addInvestmentIds("FOUSA00DOU")
                .addDataPoints("3821")
                .setStartDate("2023-01-01")
                .setEndDate("2023-12-31")
                .setRequestId("test-request-" + System.currentTimeMillis())
                .setUserId("test-user")
                .setAuthorization("Bearer test-token")
                .setProductId("test-product")
                .setCurrency("USD")
                .setUseCase("grpc-client-test")
                .build();
    }

    private void shutdownChannel(ManagedChannel channel) {
        if (channel != null && !channel.isShutdown()) {
            channel.shutdown();
            try {
                if (!channel.awaitTermination(CONNECTION_TIMEOUT_SECONDS, TimeUnit.SECONDS)) {
                    System.out.println("Channel did not terminate gracefully, forcing shutdown...");
                    channel.shutdownNow();
                    if (!channel.awaitTermination(CONNECTION_TIMEOUT_SECONDS, TimeUnit.SECONDS)) {
                        System.err.println("Channel did not terminate after forced shutdown");
                    }
                }
            } catch (InterruptedException e) {
                System.out.println("Interrupted while waiting for channel termination");
                channel.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
}
