package com.morningstar.martapi.grpc;

import com.morningstar.martapi.grpc.InvestmentResponseProto;
import com.morningstar.martapi.grpc.InvestmentProto;
import com.morningstar.martapi.grpc.InvestmentValueProto;
import com.morningstar.martapi.grpc.TimeSeriesDataPointProto;
import com.morningstar.martapi.grpc.StatusProto;
import com.morningstar.martgateway.domains.core.entity.investmentresponse.Investment;
import com.morningstar.martgateway.domains.core.entity.investmentresponse.InvestmentResponse;
import com.morningstar.martgateway.domains.core.entity.response.Status;
import com.morningstar.martcommon.entity.result.response.TimeseriesData;
import com.morningstar.martcommon.entity.result.response.TimeseriesPair;
import com.morningstar.martcommon.entity.result.DataPointError;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

public class TimeSeriesGrpcServiceImprovedTest {

    @InjectMocks
    private TimeSeriesGrpcService timeSeriesGrpcService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testConvertToInvestmentResponseProto_WithTimeSeriesData() {
        InvestmentResponse investmentResponse = createTestInvestmentResponse();
        
        InvestmentResponseProto protoResponse = timeSeriesGrpcService.convertToInvestmentResponseProto(investmentResponse);
        
        assertNotNull(protoResponse);
        assertEquals("200200", protoResponse.getStatus().getCode());
        assertEquals("Process is successful with some data points errors", protoResponse.getStatus().getMessage());
        assertEquals(2, protoResponse.getInvestmentsCount());
        
        InvestmentProto investment1 = protoResponse.getInvestments(0);
        assertEquals("E0GBR017IT", investment1.getId());
        assertEquals(1, investment1.getValuesCount());
        assertEquals(0, investment1.getErrorsCount());
        
        InvestmentValueProto value = investment1.getValues(0);
        assertEquals("HPD10", value.getDatapointId());
        assertEquals(10, value.getTimeSeriesDataCount());
        
        TimeSeriesDataPointProto firstDataPoint = value.getTimeSeriesData(0);
        assertEquals("2024-02-29", firstDataPoint.getDate());
        assertEquals("0.25376", firstDataPoint.getValue());
        
        TimeSeriesDataPointProto lastDataPoint = value.getTimeSeriesData(9);
        assertEquals("2024-11-30", lastDataPoint.getDate());
        assertEquals("-0.20715", lastDataPoint.getValue());
        
        InvestmentProto investment2 = protoResponse.getInvestments(1);
        assertEquals("F00000PKCK", investment2.getId());
        assertEquals(0, investment2.getValuesCount());
        assertEquals(1, investment2.getErrorsCount());
        assertTrue(investment2.getErrors(0).contains("HPD10"));
        assertTrue(investment2.getErrors(0).contains("403"));
    }

    @Test
    void testConvertToInvestmentResponseProto_EmptyInvestmentResponse() {
        InvestmentResponse investmentResponse = new InvestmentResponse();
        Status status = new Status("200", "Success");
        investmentResponse.setStatus(status);
        investmentResponse.setInvestments(Arrays.asList());
        
        InvestmentResponseProto protoResponse = timeSeriesGrpcService.convertToInvestmentResponseProto(investmentResponse);
        
        assertNotNull(protoResponse);
        assertEquals("200", protoResponse.getStatus().getCode());
        assertEquals("Success", protoResponse.getStatus().getMessage());
        assertEquals(0, protoResponse.getInvestmentsCount());
    }

    @Test
    void testConvertToInvestmentResponseProto_NullValues() {
        InvestmentResponse investmentResponse = new InvestmentResponse();
        
        InvestmentResponseProto protoResponse = timeSeriesGrpcService.convertToInvestmentResponseProto(investmentResponse);
        
        assertNotNull(protoResponse);
        assertEquals(0, protoResponse.getInvestmentsCount());
    }

    private InvestmentResponse createTestInvestmentResponse() {
        Status status = new Status("200200", "Process is successful with some data points errors");
        
        Investment investment1 = new Investment();
        investment1.setId("E0GBR017IT");
        
        TimeseriesData timeseriesData = new TimeseriesData();
        timeseriesData.setDatapointId("HPD10");
        
        List<TimeseriesPair> timeseriesPairs = Arrays.asList(
            new TimeseriesPair("2024-02-29", "0.25376", null),
            new TimeseriesPair("2024-03-31", "2.97969", null),
            new TimeseriesPair("2024-04-30", "2.76010", null),
            new TimeseriesPair("2024-05-31", "0.25376", null),
            new TimeseriesPair("2024-06-30", "8.06765", null),
            new TimeseriesPair("2024-07-31", "-4.16397", null),
            new TimeseriesPair("2024-08-31", "0.25376", null),
            new TimeseriesPair("2024-09-30", "-0.13255", null),
            new TimeseriesPair("2024-10-31", "3.22690", null),
            new TimeseriesPair("2024-11-30", "-0.20715", null)
        );
        timeseriesData.setTimeseriesPairList(timeseriesPairs);
        investment1.setTimeseriesDataList(Arrays.asList(timeseriesData));
        
        Investment investment2 = new Investment();
        investment2.setId("F00000PKCK");
        
        DataPointError error = new DataPointError("HPD10", "403");
        investment2.setErrors(Arrays.asList(error));
        
        InvestmentResponse response = new InvestmentResponse();
        response.setStatus(status);
        response.setInvestments(Arrays.asList(investment1, investment2));
        
        return response;
    }
}
