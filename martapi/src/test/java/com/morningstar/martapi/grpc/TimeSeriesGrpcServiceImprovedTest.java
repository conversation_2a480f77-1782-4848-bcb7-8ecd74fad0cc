package com.morningstar.martapi.grpc;

import com.morningstar.martapi.grpc.TimeSeriesServiceProto.InvestmentResponseProto;
import com.morningstar.martapi.grpc.TimeSeriesServiceProto.InvestmentProto;
import com.morningstar.martapi.grpc.TimeSeriesServiceProto.InvestmentValueProto;
import com.morningstar.martapi.grpc.TimeSeriesServiceProto.TimeSeriesDataPointProto;
import com.morningstar.martapi.grpc.TimeSeriesServiceProto.InvestmentErrorProto;
import com.morningstar.martapi.grpc.TimeSeriesServiceProto.StatusProto;
import com.morningstar.martgateway.domains.core.entity.investmentresponse.Investment;
import com.morningstar.martgateway.domains.core.entity.investmentresponse.InvestmentResponse;
import com.morningstar.martgateway.domains.core.entity.response.Status;
import com.morningstar.martcommon.entity.result.response.TimeseriesData;
import com.morningstar.martcommon.entity.result.response.TimeseriesPair;
import com.morningstar.martcommon.entity.result.DataPointError;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class to verify the improved gRPC service implementation
 * that matches the expected JSON format from weibo_aicode_gprc.txt
 */
class TimeSeriesGrpcServiceImprovedTest {

    @Test
    @DisplayName("Test improved protobuf structure matches expected JSON format")
    void testImprovedProtobufStructure() throws Exception {
        // Create test data that matches the expected JSON format
        InvestmentResponse testResponse = createTestInvestmentResponse();
        
        // Get the private conversion method using reflection
        TimeSeriesGrpcService service = new TimeSeriesGrpcService();
        Method convertMethod = TimeSeriesGrpcService.class.getDeclaredMethod(
            "convertToInvestmentResponseProto", InvestmentResponse.class);
        convertMethod.setAccessible(true);
        
        // Convert to protobuf
        InvestmentResponseProto protoResponse = (InvestmentResponseProto) convertMethod.invoke(service, testResponse);
        
        // Verify the structure matches expected format
        assertNotNull(protoResponse, "Proto response should not be null");
        
        // Verify status
        StatusProto status = protoResponse.getStatus();
        assertEquals("200200", status.getCode());
        assertEquals("Process is successful with some data points errors", status.getMessage());
        
        // Verify investments
        assertEquals(2, protoResponse.getInvestmentsCount(), "Should have 2 investments");
        
        // Verify first investment with time series data
        InvestmentProto investment1 = protoResponse.getInvestments(0);
        assertEquals("E0GBR017IT", investment1.getId());
        assertEquals(1, investment1.getValuesCount(), "Should have 1 value entry");
        assertEquals(0, investment1.getErrorsCount(), "Should have no errors");
        
        // Verify the time series data structure
        InvestmentValueProto value = investment1.getValues(0);
        assertEquals("HPD10", value.getDatapointId());
        assertEquals(10, value.getTimeSeriesDataCount(), "Should have 10 time series data points");
        
        // Verify specific time series data points
        TimeSeriesDataPointProto firstDataPoint = value.getTimeSeriesData(0);
        assertEquals("2024-02-29", firstDataPoint.getDate());
        assertEquals("0.25376", firstDataPoint.getValue());
        
        // Verify second investment with errors
        InvestmentProto investment2 = protoResponse.getInvestments(1);
        assertEquals("F00000PKCK", investment2.getId());
        assertEquals(0, investment2.getValuesCount(), "Should have no values");
        assertEquals(1, investment2.getErrorsCount(), "Should have 1 error");
        
        // Verify error structure
        InvestmentErrorProto error = investment2.getErrors(0);
        assertEquals("HPD10", error.getDatapointId());
        assertEquals("403", error.getErrorCode());
    }

    @Test
    @DisplayName("Test simplified conversion method structure")
    void testSimplifiedConversionMethod() throws Exception {
        // Verify that the conversion method is simplified and uses helper methods
        TimeSeriesGrpcService service = new TimeSeriesGrpcService();
        
        // Check that the main conversion method exists
        Method convertMethod = TimeSeriesGrpcService.class.getDeclaredMethod(
            "convertToInvestmentResponseProto", InvestmentResponse.class);
        assertNotNull(convertMethod, "Main conversion method should exist");
        
        // Check that the helper method exists
        Method helperMethod = TimeSeriesGrpcService.class.getDeclaredMethod(
            "convertInvestmentToProto", Investment.class);
        assertNotNull(helperMethod, "Helper conversion method should exist");
    }

    private InvestmentResponse createTestInvestmentResponse() {
        // Create status
        Status status = new Status("200200", "Process is successful with some data points errors");
        
        // Create first investment with time series data
        Investment investment1 = new Investment();
        investment1.setId("E0GBR017IT");
        
        TimeseriesData timeseriesData = new TimeseriesData();
        timeseriesData.setDatapointId("HPD10");
        
        List<TimeseriesPair> timeseriesPairs = Arrays.asList(
            new TimeseriesPair("2024-02-29", "0.25376", null),
            new TimeseriesPair("2024-03-31", "2.97969", null),
            new TimeseriesPair("2024-04-30", "2.76010", null),
            new TimeseriesPair("2024-05-31", "0.25376", null),
            new TimeseriesPair("2024-06-30", "8.06765", null),
            new TimeseriesPair("2024-07-31", "-4.16397", null),
            new TimeseriesPair("2024-08-31", "0.25376", null),
            new TimeseriesPair("2024-09-30", "-0.13255", null),
            new TimeseriesPair("2024-10-31", "3.22690", null),
            new TimeseriesPair("2024-11-30", "-0.20715", null)
        );
        timeseriesData.setTimeseriesPairList(timeseriesPairs);
        investment1.setTimeseriesDataList(Arrays.asList(timeseriesData));
        
        // Create second investment with errors
        Investment investment2 = new Investment();
        investment2.setId("F00000PKCK");
        
        DataPointError error = new DataPointError("HPD10", "403");
        investment2.setErrors(Arrays.asList(error));
        
        // Create response
        InvestmentResponse response = new InvestmentResponse();
        response.setStatus(status);
        response.setInvestments(Arrays.asList(investment1, investment2));
        
        return response;
    }
}
